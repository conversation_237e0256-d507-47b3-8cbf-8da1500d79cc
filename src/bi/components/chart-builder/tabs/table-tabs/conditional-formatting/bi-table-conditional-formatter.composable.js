import chroma from 'chroma-js';

export function useTableConditionalFormatter() {
  function getOperators(field) {
    if (['integer', 'numeric'].includes(field.type)) {
      return [
        { value: 'empty', label: 'Empty' },
        { value: 'not_empty', label: 'Not empty' },
        { value: 'equal', label: 'Equal to' },
        { value: 'not_equal', label: 'Not equal to' },
        { value: 'less_than', label: 'Less than' },
        { value: 'greater_than', label: 'Greater than' },
        { value: 'less_than_or_equal', label: 'Less than or equal to' },
        { value: 'greater_than_or_equal', label: 'Greater than or equal to' },
      ];
    }
    return [
      { value: 'empty', label: 'Empty' },
      { value: 'not_empty', label: 'Not empty' },
      { value: 'equal', label: 'Equal to' },
      { value: 'not_equal', label: 'Not equal to' },
      { value: 'contains', label: 'Contains' },
      { value: 'not_contains', label: 'Does not contain' },
      { value: 'starts_with', label: 'Starts with' },
      { value: 'ends_with', label: 'Ends with' },
    ];
  }

  function checkRuleMatch(rule, row_data, column_data, cell_value) {
    if (rule.apply_to === 'entire_row') {
      return checkRowMatch(rule, row_data);
    }
    else {
      if (rule.field !== column_data.field)
        return false;
      return checkCellMatch(rule, cell_value);
    }
  }

  function checkRowMatch(rule, row_data) {
    const cell_value = row_data[rule.field];
    return checkCellMatch(rule, cell_value);
  }
  function checkCellMatch(rule, cell_value) {
    if (rule.operator === 'empty') {
      return [null, undefined, '', 'null'].includes(cell_value);
    }
    else if (rule.operator === 'not_empty') {
      return ![null, undefined, '', 'null'].includes(cell_value);
    }
    if (!cell_value)
      return false;
    if (rule.operator === 'equal') {
      return cell_value === rule.value;
    }
    else if (rule.operator === 'not_equal') {
      return cell_value !== rule.value;
    }
    // For numeric operators
    else if (rule.operator === 'less_than') {
      return cell_value < rule.value;
    }
    else if (rule.operator === 'greater_than') {
      return cell_value > rule.value;
    }
    else if (rule.operator === 'less_than_or_equal') {
      return cell_value <= rule.value;
    }
    else if (rule.operator === 'greater_than_or_equal') {
      return cell_value >= rule.value;
    }
    // For text operators
    else if (rule.operator === 'contains') {
      return String(cell_value).toLowerCase().includes(String(rule.value).toLowerCase());
    }
    else if (rule.operator === 'not_contains') {
      return !String(cell_value).toLowerCase().includes(String(rule.value).toLowerCase());
    }
    else if (rule.operator === 'starts_with') {
      return String(cell_value).toLowerCase().startsWith(String(rule.value).toLowerCase());
    }
    else if (rule.operator === 'ends_with') {
      return String(cell_value).toLowerCase().endsWith(String(rule.value).toLowerCase());
    }

    return false;
  }
  function getContrastTextColor(color) {
    try {
      const white_bias = 5;
      return (chroma.contrast(color, 'white') + white_bias) >= chroma.contrast(color, 'black') ? '#FFFFFF' : '#000000';
    }
    catch (error) {
      logger.log(`[DEBUG] bi-table-conditional-formatter.composable.js::140\n${error}`);
      return '#000000';
    }
  }
  function getLighterColor(color) {
    return chroma(color).luminance(
      Math.min(1, chroma(color).luminance() + 0.2),
    ).hex();
  }
  function getColorForValue(value, min, max, baseColor, steps) {
    const colors = chroma
      .scale(['#ffffff', baseColor])
      .mode('lch')
      .colors(steps);

    let index;

    if (max === min) {
    // All values are the same — return mid color or last
      index = Math.floor(steps / 2);
    }
    else {
      const ratio = (value - min) / (max - min);
      index = Math.round(ratio * (steps - 1));
    }

    const safeIndex = Math.max(0, Math.min(index, steps - 1));
    return colors[safeIndex];
  }
  return {
    getOperators,
    checkRuleMatch,
    getContrastTextColor,
    getLighterColor,
    getColorForValue,
  };
}
