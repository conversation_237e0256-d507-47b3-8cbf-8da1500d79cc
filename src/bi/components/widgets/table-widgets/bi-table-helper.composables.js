import { nextTick } from 'vue';
import { useTableConditionalFormatter } from '~/bi/components/chart-builder/tabs/table-tabs/conditional-formatting/bi-table-conditional-formatter.composable.js';

export function useBiTableHelper() {
  const { checkRuleMatch, getContrastTextColor, getLighterColor, getColorForValue } = useTableConditionalFormatter();

  /**
   * Parse a value to a safe number, returning NaN for invalid values
   */
  function parseSafeNumber(value) {
    if (value === null || value === undefined || value === '') {
      return Number.NaN;
    }
    const num = Number(value);
    return Number.isNaN(num) ? Number.NaN : num;
  }

  /**
   * Get min and max values for a specific field from the table instance
   */
  function getMinMax(instance, rule, table_columns) {
    const columns = table_columns.map((col, index) => ({ index, field: col.field })).filter(col => col.field === rule.field);
    let values = [];
    for (const col of columns) {
      values = values.concat(instance.getDataAtCol(col.index).map(val => parseSafeNumber(val)).filter(val => !Number.isNaN(val)));
    }
    if (values.length) {
      return {
        min: Math.min(...values),
        max: Math.max(...values),
      };
    }
    return null;
  }

  /**
   * Get cell formatting based on conditional formatting rules
   */
  function getCellFormatting({ column, row_data, instance, value, chart_config, table_columns }) {
    // For total rows and columns styles
    if (row_data.__is_grand_total) {
      return {
        'background-color': '#475467',
        'font-weight': 600,
        'color': '#FFFFFF',
      };
    }
    if (row_data.__is_column_total || column?.data?.includes('__row_total_')) {
      return {
        'background-color': '#EAECF0',
        'font-weight': 600,
        'color': '#101828 !important',
      };
    }

    let rules = [];
    if (chart_config?.type) {
      rules = chart_config?.conditional_formatting || [];
    }
    const min_max_map = {};
    for (const rule of rules) {
      if (rule.formatting_style === 'single_color') {
        if (checkRuleMatch(rule, row_data, column, value)) {
          let color = rule.color;
          if (column.field !== rule.field) {
            color = getLighterColor(rule.color);
          }
          return {
            'background-color': color,
            'color': getContrastTextColor(color),
          };
        }
      }
      else {
        if (column.field !== rule.field)
          continue;
        let min = rule.start_range_value;
        let max = rule.end_range_value;
        if ((rule.start_range_at === 'min' || rule.end_range_at === 'max') && !min_max_map[column.field]) {
          min_max_map[rule.field] = getMinMax(instance, rule, table_columns);
        }
        if (rule.start_range_at === 'min') {
          min = min_max_map[rule.field]?.min;
        }
        if (rule.end_range_at === 'max') {
          max = min_max_map[rule.field]?.max;
        }
        const cell_value = parseSafeNumber(value);
        if (Number.isNaN(cell_value) || (cell_value < min || cell_value > max))
          continue;
        const color = getColorForValue(cell_value, min, max, rule.color, rule.color_range);
        return {
          'background-color': color,
          'color': getContrastTextColor(color),
        };
      }
    }
    return null;
  }

  /**
   * Auto-fit columns in the Handsontable instance
   */
  function autoFitColumns(hot, chart_config, table_columns) {
    nextTick(() => {
      const plugin = hot.getPlugin('autoColumnSize');
      plugin.recalculateAllColumnsWidth();
      const widths = [];
      for (let col = 0; col < hot.countCols(); col++) {
        if (chart_config?.type === 'table') {
          const column_key = table_columns[col].data;
          widths.push(chart_config?.columns_map?.[column_key]?.width || plugin.getColumnWidth(col));
        }
        else {
          widths.push(plugin.getColumnWidth(col));
        }
      }

      if (widths.length) {
        setTimeout(() => {
          hot.updateSettings({ colWidths: widths });
          hot.render();
        }, 100);
      }
    });
  }

  /**
   * Generate nested table headers for pivot tables
   */
  async function generateNestedTableHeaders(data, columns, values, group_by_keys, chart_config, delimiter = '|') {
    const nested_headers = [];

    // Handle case: no column pivoting
    if (columns.length === 0) {
      const flat_header_row = [];

      // Add row headers
      for (const rh of group_by_keys) {
        flat_header_row.push({ label: rh, key: rh, field: rh });
      }

      // Add values directly as columns
      for (const val of values) {
        flat_header_row.push({ label: val, key: val, field: val });
      }

      if (chart_config?.show_row_totals) {
        for (const val of values) {
          flat_header_row.push({ label: `Total ${val}`, key: `__row_total_${val}` });
        }
      }

      nested_headers.push(flat_header_row);
      return nested_headers;
    }

    // Build tree structure for column pivoting
    function buildTree(data, level = 0) {
      if (level >= columns.length)
        return [];

      const groups = {};
      for (const row of data) {
        const key = row[columns[level]];
        if (!groups[key])
          groups[key] = [];
        groups[key].push(row);
      }

      const nodes = [];
      for (const key in groups) {
        const children = buildTree(groups[key], level + 1);
        nodes.push({ label: key, children });
      }
      return nodes;
    }

    const tree = buildTree(data);

    function countLeaves(node) {
      if (!node.children || node.children.length === 0)
        return 1;
      return node.children.reduce((sum, child) => sum + countLeaves(child), 0);
    }

    // Generate nested headers
    function fillHeaders(nodes, level = 0) {
      if (!nested_headers[level]) {
        nested_headers[level] = [];
        for (let i = 0; i < group_by_keys.length; i++) {
          nested_headers[level].push({ label: '', colspan: 1 });
        }
      }

      for (const node of nodes) {
        const leaf_count = countLeaves(node);
        nested_headers[level].push({
          label: node.label,
          colspan: leaf_count * values.length,
        });

        if (node.children.length > 0) {
          fillHeaders(node.children, level + 1);
        }
      }
    }

    fillHeaders(tree);

    const final_row = [];
    for (const rh of group_by_keys) {
      final_row.push({ label: rh, key: rh, field: rh });
    }

    function pushLeafLabels(nodes, path = []) {
      for (const node of nodes) {
        const currentPath = [...path, node.label];
        if (node.children.length > 0) {
          pushLeafLabels(node.children, currentPath);
        }
        else {
          for (const val of values) {
            final_row.push({
              label: val,
              key: currentPath.concat(val).join(delimiter),
              field: val,
            });
          }
        }
      }
    }

    pushLeafLabels(tree);
    if (chart_config?.show_row_totals) {
      for (let i = 1; i <= nested_headers.length; i++) {
        nested_headers[i - 1].push({ label: i === nested_headers.length ? 'Row Total' : '', colspan: values.length });
      }
      for (const val of values) {
        final_row.push({
          label: `Total ${val}`,
          key: `__row_total_${val}`,
        });
      }
    }
    nested_headers.push(final_row);
    return nested_headers;
  }

  /**
   * Generate Handsontable data for pivot tables
   */
  function generateHandsontableData(data, columns, values, group_by_keys, chart_config, delimiter = '|') {
    // Helper: group rows recursively
    function normalizeKey(val) {
      // Consistent key format for grouping — handles undefined/null gracefully
      return val === undefined || val === null ? '' : String(val);
    }

    function getRowKeys(row) {
      const column_key = columns.length
        ? columns.map(col => normalizeKey(row[col])).join(delimiter)
        : '';
      const row_keys = [];
      for (const val of values) {
        if (column_key.length) {
          row_keys.push({ data_key: `${column_key}${delimiter}${val}`, value_key: val });
        }
        else {
          row_keys.push({ data_key: val, value_key: val });
        }
      }
      return row_keys;
    }

    function formatRow(row, node = {}) {
      const row_keys = getRowKeys(row);
      for (const key of row_keys) {
        node[key.data_key] = row[key.value_key] ?? null;
        node[key.value_key] = node[key.data_key];
      }
      node.__actual_row = row;

      if (chart_config?.show_row_totals) {
        for (const val of values) {
          node[`__row_total_${val}`] = row[val] ?? null;
        }
      }

      return node;
    }

    function groupByHeaders(rows, headers) {
      if (headers.length === 0) {
        return rows.map(row => formatRow(row));
      }

      const [current, ...rest] = headers;
      const groups = {};

      rows.forEach((row) => {
        const key = normalizeKey(row[current]);
        if (!groups[key]) {
          groups[key] = [];
        }
        groups[key].push(row);
      });

      return Object.entries(groups).map(([key, grouped_rows]) => {
        const node = { [current]: key };

        if (rest.length > 0) {
          node.__is_group = true;
          node.__is_group_column = current;
          node.__children = groupByHeaders(grouped_rows, rest);

          if (chart_config?.show_column_totals || chart_config?.show_row_totals) {
            for (const row of grouped_rows) {
              if (chart_config?.show_column_totals) {
                const row_keys = getRowKeys(row);
                for (const key of row_keys) {
                  const existing = Number(node[key.data_key] ?? 0);
                  const current = Number(row[key.value_key] ?? 0);
                  const sum = existing + (Number.isNaN(current) ? 0 : current);
                  node[key.data_key] = String(sum);
                  node.__is_column_total = true;
                }
              }

              if (chart_config?.show_row_totals) {
                for (const val of values) {
                  const existing = Number(node[`__row_total_${val}`] ?? 0);
                  const current = Number(row[val] ?? 0);
                  const sum = existing + (Number.isNaN(current) ? 0 : current);
                  node[`__row_total_${val}`] = String(sum);
                }
              }
            }
          }
        }
        else {
          node.__is_leaf_group = true;

          for (const row of grouped_rows) {
            const row_keys = getRowKeys(row);

            // aggregate value columns
            for (const key of row_keys) {
              const existing = Number(node[key.data_key] ?? 0);
              const current = Number(row[key.value_key] ?? 0);
              const sum = existing + (Number.isNaN(current) ? 0 : current);
              node[key.data_key] = String(sum);
              node[key.value_key] = String(sum);
            }

            // aggregate row totals if enabled
            if (chart_config?.show_row_totals) {
              for (const val of values) {
                const existing = Number(node[`__row_total_${val}`] ?? 0);
                const current = Number(row[val] ?? 0);
                const sum = existing + (Number.isNaN(current) ? 0 : current);
                node[`__row_total_${val}`] = String(sum);
              }
            }
          }
        }

        return node;
      });
    }

    const nested_data = groupByHeaders(data, group_by_keys);

    // --- Grand Total Column ---
    if (chart_config?.show_grand_totals) {
      const grand_total = { [group_by_keys[0] || 'Total']: 'Grand Totals', __is_grand_total: true };

      for (const row of data) {
      // aggregate per-column values
        const row_keys = getRowKeys(row);
        for (const key of row_keys) {
          const existing = Number(grand_total[key.data_key] ?? 0);
          const current = Number(row[key.value_key] ?? 0);
          grand_total[key.data_key] = existing + (Number.isNaN(current) ? 0 : current);
          grand_total[key.data_key] = String(grand_total[key.data_key]);
        }

        // aggregate per-row totals
        for (const val of values) {
          const existing = Number(grand_total[`__row_total_${val}`] ?? 0);
          const current = Number(row[val] ?? 0);
          grand_total[`__row_total_${val}`] = existing + (Number.isNaN(current) ? 0 : current);
          grand_total[`__row_total_${val}`] = String(grand_total[`__row_total_${val}`]);
        }
      }
      nested_data.push(grand_total);
    }

    return nested_data;
  }

  return {
    parseSafeNumber,
    getMinMax,
    getCellFormatting,
    autoFitColumns,
    generateNestedTableHeaders,
    generateHandsontableData,
  };
}
