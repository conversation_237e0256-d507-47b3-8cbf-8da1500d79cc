<script setup>
import { cloneDeep, get, orderBy } from 'lodash-es';
import { computed, onMounted, reactive, watch } from 'vue';
import BiHandsontable from '~/bi/components/widgets/table-widgets/bi-handsontable.vue';
import { useBiTableHelper } from '~/bi/composables/bi-table-helper.composables.js';
import { useBiStore } from '~/bi/store/bi.store';

const props = defineProps({
  id: {
    type: String,
    default: () => crypto.randomUUID(),
  },
  data: {
    type: Array,
    default: null,
  },
  tableMetadata: {
    type: Object,
    default: () => ({}),
  },
  config: {
    type: Object,
    default: () => ({}),
  },
  isBuilder: {
    type: Boolean,
    default: true,
  },
  isEditing: {
    type: Boolean,
    default: true,
  },
  height: {
    type: Number,
    default: 0,
  },
});

const bi_store = useBiStore();
const { getCellFormatting, autoFitColumns, generateNestedTableHeaders, generateHandsontableData } = useBiTableHelper();

const state = reactive({
  loading: false,
  error: null,
  data: [],
  columns: [],

  table_columns: [],
  table_data: [],
  nested_headers: [],
  nested_rows: false,
  column_config: {},

  table_instance: null,

  changes_detected: false,
  loading_changes: false,
  force_update: 1, // Will be updated in the future to update settings
});

const chart_config = computed(() => {
  const chart_details = props.config?.chart || {};
  const {
    type,

    columns_map = {},
    show_row_headers = false,

    pivot_rows = [],
    pivot_columns = [],
    pivot_values = [],
    show_row_totals = false,
    show_column_totals = false,
    show_grand_totals = false,

    conditional_formatting = [],
  } = chart_details;

  if (type === 'table') {
    return { type, columns_map, conditional_formatting, show_row_headers };
  }
  else if (type === 'pivot_table') {
    return { type, pivot_rows, pivot_columns, pivot_values, show_row_totals, show_grand_totals, show_column_totals, conditional_formatting };
  }
  return chart_details;
});
const enable_row_headers = computed(() => {
  if (chart_config.value?.type === 'table')
    return chart_config.value?.show_row_headers || false;
  return false;
});
const table_height = computed(() => {
  if (props.height)
    return props.height;
  // Header and Footer is about 85 each total 190
  // Additional table padding 40
  const available_height = window.innerHeight - 220;
  return available_height;
});
// -------------------------------- Methods --------------------------------- //
function getTableColumn(col) {
  const is_table = chart_config.value?.type === 'table';

  // Handle the case when `col` is a string
  if (typeof col === 'string') {
    const width = is_table ? chart_config.value?.columns_map?.[col]?.width ?? null : null;

    return {
      label: col,
      data: col,
      type: 'text',
      readOnly: true,
      field: col,
      ...(width && { width }),
    };
  }

  // When `col` is an object
  const key = col.key || col.label;
  const field = col.field || key;
  const width = is_table ? chart_config.value?.columns_map?.[key]?.width ?? null : null;

  return {
    label: col.label,
    data: key,
    type: 'text',
    readOnly: true,
    field,
    ...(width && { width }),
  };
}

async function setupPivotTable(reinitialize = true) {
  if (props.isEditing && reinitialize) {
    const current_columns = state.columns;

    // Previous config columns
    const prev_rows = chart_config.value.pivot_rows || [];
    const prev_cols = chart_config.value.pivot_columns || [];
    const prev_vals = chart_config.value.pivot_values || [];

    const alias_map = bi_store.alias_to_column_mapping();

    const previous_columns = [...prev_rows, ...prev_cols, ...prev_vals];

    const current_set = new Set(current_columns);
    const previous_set = new Set(previous_columns);

    const has_column_changes = (
      current_columns.length !== previous_columns.length
      || current_columns.some(col => !previous_set.has(col))
      || previous_columns.some(col => !current_set.has(col))
    );

    if (has_column_changes) {
      const updated_rows = [];
      const updated_cols = [];
      const updated_vals = [];

      // Re-add existing ones (that still exist)
      for (const col of prev_rows) {
        if (current_set.has(col))
          updated_rows.push(col);
      }
      for (const col of prev_cols) {
        if (current_set.has(col))
          updated_cols.push(col);
      }
      for (const col of prev_vals) {
        if (current_set.has(col))
          updated_vals.push(col);
      }

      // Identify new columns
      const new_columns = current_columns.filter(col => !previous_set.has(col));

      // Classify and assign new columns
      const new_value_keys = [];
      const new_non_value_keys = [];

      for (const col of new_columns) {
        const details = alias_map[col];
        if (details?.is_aggregation || ['integer', 'numeric'].includes(details?.type)) {
          new_value_keys.push(col);
        }
        else {
          new_non_value_keys.push(col);
        }
      }

      // Assign new value keys
      updated_vals.push(...new_value_keys);

      // Assign new non-value keys by balancing between rows and columns
      for (const col of new_non_value_keys) {
        if (updated_rows.length <= updated_cols.length) {
          updated_rows.push(col);
        }
        else {
          updated_cols.push(col);
        }
      }

      bi_store.widget_builder_config.chart.pivot_rows = updated_rows;
      bi_store.widget_builder_config.chart.pivot_columns = updated_cols;
      bi_store.widget_builder_config.chart.pivot_values = updated_vals;
    }
  }

  const { pivot_rows = [], pivot_columns = [], pivot_values = [] } = chart_config.value;

  const nested_headers = await generateNestedTableHeaders(
    state.data,
    pivot_columns,
    pivot_values,
    pivot_rows,
    chart_config.value,
  );

  state.table_columns = nested_headers.slice(-1)[0].map(col => getTableColumn(col));
  state.nested_headers = nested_headers;

  const nested_data = await generateHandsontableData(
    state.data,
    pivot_columns,
    pivot_values,
    pivot_rows,
    chart_config.value,
  );

  if (pivot_rows?.length)
    state.nested_rows = true;

  state.table_data = nested_data;
}

async function setupTable(reinitialize = true) {
  if (!props.isBuilder && props.isEditing && reinitialize) {
    const columns_map = state.columns.reduce((acc, col_key, index) => {
      const col_data = bi_store.widget_builder_config.chart.columns_map?.[col_key] || {};
      acc[col_key] = {
        key: col_key,
        width: get(col_data, 'width', null),
        visible: get(col_data, 'visible', true),
        order_index: get(col_data, 'order_index', index),
      };
      return acc;
    }, {});
    bi_store.widget_builder_config.chart.columns_map = cloneDeep(columns_map);
  }

  if (props.isBuilder) {
    state.table_columns = state.columns.map(col => getTableColumn(col));
  }
  else {
    state.table_columns = orderBy(
      state.columns.filter(col =>
        get(chart_config.value?.columns_map || {}, `${col}.visible`, true),
      ),
      col => get(chart_config.value?.columns_map || {}, `${col}.order_index`, 0),
    ).map(col => getTableColumn(col));
  }

  state.table_data = state.data;
  if (props.isBuilder) {
    state.column_config = state.columns.reduce((acc, col) => {
      const column_details = bi_store.alias_to_column_mapping()[col];
      if (column_details?.is_aggregation) {
        acc[col] = { backgroundColor: '#FFFAEB' };
      }
      return acc;
    }, {});
  }
}

async function initializeTable(reinitialize = true) {
  state.data = props.data;
  state.columns = (props.tableMetadata?.columns || [])?.map(col => col.name);

  if (chart_config.value?.type === 'pivot_table') {
    await setupPivotTable(reinitialize);
  }
  else {
    await setupTable(reinitialize);
  }
  bi_store.is_table_dirty = false;
}

function handleColumnResize(col_widths_map) {
  if (!props.isEditing)
    return;
  if (chart_config.value.type === 'table') {
    Object.entries(col_widths_map).forEach(([col_key, width]) => {
      if (bi_store.widget_builder_config.chart.columns_map?.[col_key]) {
      // Update width in the columns map
        bi_store.widget_builder_config.chart.columns_map[col_key] = {
          ...bi_store.widget_builder_config.chart.columns_map[col_key],
          width,
        };
      }
    });
  }
}

// Helper functions that use the composable with current context
function getCellFormattingWithContext(params) {
  return getCellFormatting({
    ...params,
    chart_config: chart_config.value,
    table_columns: state.table_columns,
  });
}

function autoFitColumnsWithContext(hot) {
  return autoFitColumns(hot, chart_config.value, state.table_columns);
}

watch(() => (bi_store.is_table_dirty), () => {
  if (!props.isEditing)
    return;
  // state.changes_detected = true;
  initializeTable(false);
  state.force_update++;
});
// -------------------------------- Lifecycle ------------------------------- //
onMounted(() => {
  initializeTable();
});
</script>

<template>
  <div class="h-[100%] w-full">
    <div
      v-if="state.changes_detected"
      class="flex items-center justify-center w-full h-full"
    >
      <!-- Popup card -->
      <div class="bg-white rounded-lg shadow-lg border border-gray-200 p-6 flex flex-col items-center min-w-[600px]">
        <HawkFeaturedIcon theme="light-circle-outline" size="md" color="primary" class="mb-4">
          <IconHawkInfoCircle />
        </HawkFeaturedIcon>

        <div class="text-sm font-semibold mb-1">
          {{ $t('Changes detected') }}
        </div>

        <div class="text-sm text-gray-600 mb-6">
          {{ $t('New changes is been detected. You can reload to view new changes.') }}
        </div>

        <HawkButton :loading="state.loading_changes" @click="fetchTableData(bi_store.is_table_dirty)">
          <IconHawkRefreshCwTwo />
          <span class="text-sm font-medium">
            {{ $t('Load Preview') }}
          </span>
        </HawkButton>
      </div>
    </div>
    <div v-else-if="!state.table_data?.length">
      <div class="flex items-center justify-center w-full h-full">
        <HawkIllustrations type="no-data" for="bi-table" />
      </div>
    </div>
    <BiHandsontable
      v-else
      :key="state.force_update"
      :bi-table-id="id"
      :height="table_height"
      :data="state.table_data"
      :columns="state.table_columns"
      :column-config="state.column_config"
      :nested-headers="state.nested_headers"
      :nested-rows="state.nested_rows"
      :show-skeleton-loader="state.loading"
      :get-cell-formatting="getCellFormattingWithContext"
      :row-headers="enable_row_headers"
      class="h-full border"
      :enable-column-sorting="chart_config.type !== 'pivot_table'"
      @table-instance="state.table_instance = $event"
      @after-load-data="autoFitColumnsWithContext"
      @column-resize="handleColumnResize"
    />
  </div>
</template>

<style scoped lang="scss">
// Add any custom styles here
</style>
