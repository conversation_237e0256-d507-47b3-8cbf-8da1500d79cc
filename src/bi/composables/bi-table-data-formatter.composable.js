import dayjs from 'dayjs';
import timezone from 'dayjs/plugin/timezone';
import utc from 'dayjs/plugin/utc';
import { useAuthStore } from '~/auth/stores/auth.store';

dayjs.extend(utc);
dayjs.extend(timezone);

export function useBiDataFormatter() {
  const auth_store = useAuthStore();

  // Pastel colors for array tags (5 consistent colors)
  const pastel_colors = [
    '#FFE4E1', // Light pink
    '#E1F5FE', // Light blue
    '#F3E5F5', // Light purple
    '#E8F5E8', // Light green
    '#FFF3E0', // Light orange
  ];

  /**
   * Get user's timezone, fallback to browser timezone
   */
  function get_user_timezone() {
    return auth_store?.logged_in_user_details?.timezone || dayjs.tz.guess();
  }

  /**
   * Format number with thousand separator
   */
  function format_number(value, precision = null) {
    if (value === null || value === undefined || value === '') {
      return value;
    }

    const num = Number(value);
    if (Number.isNaN(num)) {
      return value;
    }

    // Apply precision for floats
    let formatted_num = num;
    if (precision !== null) {
      formatted_num = Number(num.toFixed(precision));
    }

    // Add thousand separator
    return formatted_num.toLocaleString('en-US', {
      minimumFractionDigits: precision === null ? 0 : precision,
      maximumFractionDigits: precision === null ? (Number.isInteger(num) ? 0 : 2) : precision,
    });
  }

  /**
   * Format integer with thousand separator
   */
  function format_integer(value) {
    return format_number(value, 0);
  }

  /**
   * Format float with precision rounded to 2 and thousand separator
   */
  function format_float(value) {
    return format_number(value, 2);
  }

  /**
   * Format date as DD MMM YYYY in user's timezone
   */
  function format_date(value) {
    if (!value) {
      return value;
    }

    try {
      const user_tz = get_user_timezone();
      const date = dayjs(value).tz(user_tz);

      if (!date.isValid()) {
        return value;
      }

      return date.format('DD MMM YYYY');
    }
    catch (error) {
      console.warn('Error formatting date:', error);
      return value;
    }
  }

  /**
   * Format timestamp as DD MMM YYYY HH:MM A in user's timezone
   */
  function format_timestamp(value) {
    if (!value) {
      return value;
    }

    try {
      const user_tz = get_user_timezone();
      const date = dayjs(value).tz(user_tz);

      if (!date.isValid()) {
        return value;
      }

      return date.format('DD MMM YYYY hh:mm A');
    }
    catch (error) {
      console.warn('Error formatting timestamp:', error);
      return value;
    }
  }

  /**
   * Get consistent color for array item based on index
   */
  function get_tag_color(index) {
    return pastel_colors[index % pastel_colors.length];
  }

  /**
   * Format array as HTML tags with consistent colors
   */
  function format_array_as_tags(value) {
    if (!Array.isArray(value) || value.length === 0) {
      return value;
    }

    const tags = value.map((item, index) => {
      const color = get_tag_color(index);
      const text_color = '#374151'; // Dark gray text for readability

      return `<span style="
        display: inline-block;
        background-color: ${color};
        color: ${text_color};
        padding: 2px 8px;
        margin: 1px 2px;
        border-radius: 12px;
        font-size: 11px;
        font-weight: 500;
        white-space: nowrap;
      ">${String(item)}</span>`;
    });

    return `<div style="display: flex; flex-wrap: wrap; gap: 2px; align-items: center;">${tags.join('')}</div>`;
  }

  /**
   * Detect data type based on value and optional field metadata
   */
  function detectDataType(value, fieldMetadata = {}) {
    // Use explicit field type if provided
    if (fieldMetadata.type) {
      return fieldMetadata.type;
    }

    // Auto-detect based on value
    if (value === null || value === undefined) {
      return 'text';
    }

    if (Array.isArray(value)) {
      return 'array';
    }

    if (typeof value === 'number') {
      return Number.isInteger(value) ? 'integer' : 'float';
    }

    if (typeof value === 'string') {
      // Check if it's a date/timestamp string
      const dateValue = dayjs(value);
      if (dateValue.isValid()) {
        // Check if it includes time information
        if (value.includes('T') || value.includes(':')) {
          return 'timestamp';
        }
        return 'date';
      }

      // Check if it's a numeric string
      const numValue = Number(value);
      if (!Number.isNaN(numValue)) {
        return Number.isInteger(numValue) ? 'integer' : 'float';
      }
    }

    return 'text';
  }

  /**
   * Format value based on detected or specified data type
   */
  function formatValue(value, fieldMetadata = {}) {
    const dataType = detectDataType(value, fieldMetadata);

    switch (dataType) {
      case 'integer':
        return formatInteger(value);
      case 'float':
      case 'numeric':
        return formatFloat(value);
      case 'date':
        return formatDate(value);
      case 'timestamp':
      case 'datetime':
        return formatTimestamp(value);
      case 'array':
        return formatArrayAsTags(value);
      default:
        return value;
    }
  }

  /**
   * Check if a value should be formatted as HTML (for arrays)
   */
  function isHtmlFormatted(value, fieldMetadata = {}) {
    const dataType = detectDataType(value, fieldMetadata);
    return dataType === 'array';
  }

  return {
    formatNumber,
    formatInteger,
    formatFloat,
    formatDate,
    formatTimestamp,
    formatArrayAsTags,
    formatValue,
    detectDataType,
    isHtmlFormatted,
    getTagColor,
    getUserTimezone,
  };
}
